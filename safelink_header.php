<?php defined('ABSPATH') || exit; ?>
<style>
.ad-banner {
    width: 300px;
    height: 250px;
    margin: 20px auto;
}

.ad-placeholder {
    width: 100%;
    height: 100%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ccc;
}

.timer-section {
    display: flex;
    justify-content: center;
    margin: 20px 0;
}

.timer-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: #007bff;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    font-weight: bold;
}

.content {
    margin: 40px 0;
    text-align: center;
}

.hidden {
    display: none;
}

.robot-button {
    text-align: center;
    margin: 20px 0;
}

.adclick-button {
    text-align: center;
    margin: 20px 0;
}

.info-button {
    text-align: center;
    margin: 20px 0;
}

button {
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 5px;
}

button:hover {
    background-color: #218838;
}
</style>

<?php
// Retrieve User Link from Cookies
$userLinkValue = isset($_COOKIE['userLink']) ? htmlspecialchars($_COOKIE['userLink']) : null;
?>

<script>
// Get page number from sessionStorage or set it to 1 if not found
let pageNo = sessionStorage.getItem('pageNo') || 1;

// Store the page number back in sessionStorage
sessionStorage.setItem('pageNo', pageNo);

// Display current page number in console for debugging
console.log("Current page number: " + pageNo);
</script>

<?php if ($userLinkValue): ?>
    <div class="container">
        <center><p id="pageNoDisplay">Page no: Loading...</p> </center>

        <div class="timer-section">
            <button class="btnx tp-btn" style="font-size:16px;"> hey <span style="color:red;"> click on the Image </span> <span class="blink">☝️ 👇</span>  Wait 15 Second....</button>
        </div>
        <div id="info-button" class="info-button hidden">
            <h4 class="tp-text" style="font-family: 'Open Sans', sans-serif; font-size: 16px; margin: 15px 0px; display: block; border: 1px solid; border-radius: 15px;">
                please wait  & FOR
            </h4>
            <span id="timer">20</span>
            <span style="color:#37B5FE;">seconds</span>
        </div>

        <div id="adclick-button" class="adclick-button hidden">
            <h4 class="tp-text" style="font-family: 'Open Sans', sans-serif; font-size: 16px; margin: 15px 0px; display: block; border: 1px solid; border-radius: 15px;">
                Scroll Down & Click On <b><span style="color:#37B5FE;">Continue</span></b> Button For Your Destination Link
            </h4>
        </div>
        <div id="robot-button" class="robot-button hidden">
            <button onclick="scrollToFooter()">I am not a robot</button>
        </div>
    </div>

    <script>
    document.getElementById('pageNoDisplay').textContent = "current on Page no: " + pageNo + " / 3";
    if (pageNo >= 2) {
        document.querySelector('.timer-section').style.display = 'none';
        const infoButton = document.getElementById('info-button');
        infoButton.classList.remove('hidden');
    }

    function getRandomValue() {
        const values = [3, 2, 1];
        return values[Math.floor(Math.random() * values.length)];
    }

    const result = getRandomValue();
    const button = document.querySelector('.btnx');
    let countdown = 15; // 15 second countdown

    function updateButtonText() {
        if (result === 1) {
            button.innerHTML = `hey <span style="color:red;"> click on the Image </span> <span class="blink">☝️ 👇</span> and Wait for <span style="color:blue; font-weight:bold;">${countdown}</span> Second....`;
        } else {
            button.innerHTML = `hey <span style="color:red;"> please </span> <span class="blink">☝️ 👇</span> Wait for <span style="color:blue; font-weight:bold;">${countdown}</span> Second....`;
        }
    }

    // Initial button text with countdown
    updateButtonText();

    // Start countdown timer
    const countdownInterval = setInterval(() => {
        countdown--;
        updateButtonText();

        if (countdown <= 0) {
            clearInterval(countdownInterval);
            // Update button text when countdown is finished
            if (result === 1) {
                button.innerHTML = 'hey <span style="color:green;"> You can now click on the Image </span> <span class="blink">☝️ 👇</span> Continue....';
            } else {
                button.innerHTML = 'hey <span style="color:green;"> You can now proceed </span> <span class="blink">☝️ 👇</span> Continue....';
            }
        }
    }, 1000); // Update every second
    </script>

    <center>
        <?php renderAdHook('adsence_header_up'); ?>
    </center>

<?php else: ?>
    <!-- User Link Missing -->
<?php endif; ?>
